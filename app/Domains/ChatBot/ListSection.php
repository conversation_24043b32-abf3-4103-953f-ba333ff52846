<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;
use InvalidArgumentException;

class ListSection
{
    public ?int $id;
    public ?int $interactive_message_id;
    public ?string $title;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    /** @var ListRow[]|null $rows */
    public ?array $rows;

    public function __construct(
        ?int $id = null,
        ?int $interactive_message_id = null,
        ?string $title = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?array $rows = null
    ) {
        $this->id = $id;
        $this->interactive_message_id = $interactive_message_id;
        $this->title = $title;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->rows = $rows;

        $this->validateRows();
    }

    /**
     * Validate rows in this section
     */
    private function validateRows(): void
    {
        if (!$this->rows) {
            return;
        }

        foreach ($this->rows as $row) {
            if (!$row instanceof ListRow) {
                throw new InvalidArgumentException('All rows must be ListRow instances');
            }
        }

        // WhatsApp API requires at least 1 row per section
        if (count($this->rows) === 0) {
            throw new InvalidArgumentException('Section must have at least 1 row');
        }
    }

    /**
     * Add row to this section
     */
    public function addRow(ListRow $row): void
    {
        if (!$this->rows) {
            $this->rows = [];
        }

        $this->rows[] = $row;
        $this->validateRows();
    }

    /**
     * Remove row from this section
     */
    public function removeRow(int $index): void
    {
        if (!$this->rows || !isset($this->rows[$index])) {
            throw new InvalidArgumentException('Row index does not exist');
        }

        array_splice($this->rows, $index, 1);
        $this->validateRows();
    }

    /**
     * Get row count
     */
    public function getRowCount(): int
    {
        return count($this->rows ?? []);
    }

    /**
     * Convert to WhatsApp API payload
     */
    public function toWhatsAppPayload(): array
    {
        $payload = [];

        if ($this->title) {
            $payload['title'] = $this->title;
        }

        if ($this->rows) {
            $payload['rows'] = [];
            foreach ($this->rows as $row) {
                $payload['rows'][] = $row->toWhatsAppPayload();
            }
        }

        return $payload;
    }

    /**
     * Check if section is valid
     */
    public function isValid(): bool
    {
        try {
            $this->validateRows();
            return true;
        } catch (InvalidArgumentException $e) {
            return false;
        }
    }

    /**
     * Get validation errors
     */
    public function getValidationErrors(): array
    {
        $errors = [];

        try {
            $this->validateRows();
        } catch (InvalidArgumentException $e) {
            $errors[] = $e->getMessage();
        }

        if (!$this->rows || count($this->rows) === 0) {
            $errors[] = 'Section must have at least 1 row';
        }

        return $errors;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'interactive_message_id' => $this->interactive_message_id,
            'title' => $this->title,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'rows' => $this->rows ? array_map(fn($row) => $row->toArray(), $this->rows) : null,
            'row_count' => $this->getRowCount(),
            'is_valid' => $this->isValid(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'interactive_message_id' => $this->interactive_message_id,
            'title' => $this->title,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'title' => $this->title,
        ];
    }
}
