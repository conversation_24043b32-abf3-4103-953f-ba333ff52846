<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\ListRow;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;
use InvalidArgumentException;

class ListRowTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(ListRow::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->list_section_id);
        $this->assertEquals('row_1', $domain->row_id);
        $this->assertEquals('Test Row', $domain->title);
        $this->assertEquals('Test Description', $domain->description);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_valid_row_creation()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Valid Title',
            description: 'Valid description'
        );

        $this->assertTrue($row->isValid());
        $this->assertEmpty($row->getValidationErrors());
    }

    public function test_title_length_validation_success()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: '24 character title!!', // Exactly 24 characters
            description: 'Valid description'
        );

        $this->assertTrue($row->isValid());
    }

    public function test_title_length_validation_failure()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Row title cannot exceed 24 characters');

        new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'This title is way too long and exceeds the 24 character limit',
            description: 'Valid description'
        );
    }

    public function test_description_length_validation_success()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Valid Title',
            description: 'This is a valid description that is exactly 72 characters long!!' // Exactly 72 characters
        );

        $this->assertTrue($row->isValid());
    }

    public function test_description_length_validation_failure()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Row description cannot exceed 72 characters');

        new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Valid Title',
            description: 'This description is way too long and definitely exceeds the 72 character limit that is imposed by WhatsApp API'
        );
    }

    public function test_row_id_length_validation_failure()
    {
        $longRowId = str_repeat('a', 201); // 201 characters

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Row ID cannot exceed 200 characters');

        new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: $longRowId,
            title: 'Valid Title',
            description: 'Valid description'
        );
    }

    public function test_generate_row_id_from_title()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: null,
            title: 'Test Option',
            description: 'Test description'
        );

        $generatedId = $row->generateRowId();

        $this->assertStringStartsWith('row_test_option_', $generatedId);
        $this->assertLessThanOrEqual(200, strlen($generatedId));
    }

    public function test_generate_row_id_without_title()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: null,
            title: null,
            description: 'Test description'
        );

        $generatedId = $row->generateRowId();

        $this->assertStringStartsWith('row_', $generatedId);
        $this->assertLessThanOrEqual(200, strlen($generatedId));
    }

    public function test_generate_row_id_truncates_long_ids()
    {
        $longTitle = str_repeat('verylongtitle', 20); // Very long title
        
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: null,
            title: $longTitle,
            description: 'Test description'
        );

        $generatedId = $row->generateRowId();

        $this->assertLessThanOrEqual(200, strlen($generatedId));
        $this->assertEquals(200, strlen($generatedId)); // Should be exactly 200 after truncation
    }

    public function test_set_title_with_validation()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Original Title',
            description: 'Test description'
        );

        $row->setTitle('New Title');
        $this->assertEquals('New Title', $row->title);

        $this->expectException(InvalidArgumentException::class);
        $row->setTitle('This title is way too long and exceeds the limit');
    }

    public function test_set_description_with_validation()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Test Title',
            description: 'Original description'
        );

        $row->setDescription('New description');
        $this->assertEquals('New description', $row->description);

        $row->setDescription(null);
        $this->assertNull($row->description);

        $this->expectException(InvalidArgumentException::class);
        $row->setDescription('This description is way too long and definitely exceeds the 72 character limit');
    }

    public function test_set_row_id_with_validation()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'original_id',
            title: 'Test Title',
            description: 'Test description'
        );

        $row->setRowId('new_id');
        $this->assertEquals('new_id', $row->row_id);

        $this->expectException(InvalidArgumentException::class);
        $row->setRowId(str_repeat('a', 201));
    }

    public function test_whatsapp_payload_generation()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Test Option',
            description: 'Test description'
        );

        $payload = $row->toWhatsAppPayload();

        $this->assertEquals('option_1', $payload['id']);
        $this->assertEquals('Test Option', $payload['title']);
        $this->assertEquals('Test description', $payload['description']);
    }

    public function test_whatsapp_payload_generation_without_description()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Test Option',
            description: null
        );

        $payload = $row->toWhatsAppPayload();

        $this->assertEquals('option_1', $payload['id']);
        $this->assertEquals('Test Option', $payload['title']);
        $this->assertArrayNotHasKey('description', $payload);
    }

    public function test_validation_errors_for_missing_title()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: null,
            description: 'Test description'
        );

        $this->assertFalse($row->isValid());
        $errors = $row->getValidationErrors();
        $this->assertContains('Title is required', $errors);
    }

    public function test_to_store_array()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Test Option',
            description: 'Test description'
        );

        $storeArray = $row->toStoreArray();

        $this->assertEquals(1, $storeArray['list_section_id']);
        $this->assertEquals('option_1', $storeArray['row_id']);
        $this->assertEquals('Test Option', $storeArray['title']);
        $this->assertEquals('Test description', $storeArray['description']);
        $this->assertArrayNotHasKey('id', $storeArray);
    }

    public function test_to_update_array()
    {
        $row = new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'option_1',
            title: 'Test Option',
            description: 'Test description'
        );

        $updateArray = $row->toUpdateArray();

        $this->assertEquals('option_1', $updateArray['row_id']);
        $this->assertEquals('Test Option', $updateArray['title']);
        $this->assertEquals('Test description', $updateArray['description']);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('list_section_id', $updateArray);
    }

    protected function createDomainInstance()
    {
        return new ListRow(
            id: 1,
            list_section_id: 1,
            row_id: 'row_1',
            title: 'Test Row',
            description: 'Test Description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'list_section_id',
            'row_id',
            'title',
            'description',
            'created_at',
            'updated_at',
            'is_valid',
            'title_length',
            'description_length',
            'row_id_length',
        ];
    }
}
